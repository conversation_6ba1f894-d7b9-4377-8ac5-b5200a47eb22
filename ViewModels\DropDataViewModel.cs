using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Prism.Commands;
using Prism.Mvvm;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.ViewModels
{
    public class DropDataViewModel : BindableBase
    {
        private readonly DataService _dataService;
        private FieldVisit? _selectedFieldVisit;
        private string _numericInput = string.Empty;
        private string _visitNumber = string.Empty;
        private string _driverContract = string.Empty;
        private bool _isVisitNumberDuplicate = false;
        private DateTime _addDate = DateTime.Now;
        private string _hijriDate = string.Empty;
        private DateTime _departureDate = DateTime.Now;
        private DateTime _returnDate = DateTime.Now;
        private int _daysCount = 1;
        private string _missionPurpose = string.Empty;
        private Sector? _selectedSector;
        private int _visitorsCount = 1;
        private string _statusMessage = "������ ���� ���������";
        private string _saveButtonText = "����� �������";
        private string _currentDate = DateTime.Now.ToString("dddd� dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
        private string _currentTime = DateTime.Now.ToString("HH:mm:ss");
        private int _projectsCount = 1;
        private string _securityRoute = string.Empty;
        private string _visitNotes = string.Empty;

        public DropDataViewModel()
        {


            try
            {
                System.Diagnostics.Debug.WriteLine("Initializing DropDataViewModel...");

                _dataService = new DataService();
                // Subscribe to database refresh event
                DriverManagementSystem.ViewModels.DashboardViewModel.DatabaseRefreshed += OnDatabaseRefreshed;
                System.Diagnostics.Debug.WriteLine("DataService created");

                // Initialize collections
                FieldVisits = new ObservableCollection<FieldVisit>();
                Sectors = new ObservableCollection<Sector>();
                SectorOfficers = new ObservableCollection<Officer>();
                VisitorInputs = new ObservableCollection<VisitorInput>();
                ItineraryDays = new ObservableCollection<ItineraryDay>();
                Projects = new ObservableCollection<Project>();
                ProjectInputs = new ObservableCollection<ProjectInput>();
                System.Diagnostics.Debug.WriteLine("Collections initialized");

                // Initialize commands
                AddCommand = new DelegateCommand(AddFieldVisit, CanExecuteAdd);
                SaveCommand = new DelegateCommand(SaveOrUpdateFieldVisit, CanExecuteSaveOrUpdate);
                DeleteCommand = new DelegateCommand(DeleteFieldVisit, CanExecuteDelete);
                IncreaseVisitorsCommand = new DelegateCommand(IncreaseVisitors);
                DecreaseVisitorsCommand = new DelegateCommand(DecreaseVisitors, CanDecreaseVisitors);
                ClearAllDataCommand = new DelegateCommand(ClearAllData);
                IncreaseProjectsCommand = new DelegateCommand(IncreaseProjects);
                DecreaseProjectsCommand = new DelegateCommand(DecreaseProjects, CanDecreaseProjects);
                AddNewProjectCommand = new DelegateCommand(AddNewProject);
                SearchProjectCommand = new DelegateCommand<ProjectInput>(SearchProjectByNumber);
                IncreaseItineraryCommand = new DelegateCommand(IncreaseItinerary);
                DecreaseItineraryCommand = new DelegateCommand(DecreaseItinerary, CanDecreaseItinerary);
                SetApprovalCommand = new DelegateCommand<string>(SetApproval);
                ImportExcelCommand = new DelegateCommand(ImportExcelFile);
                ShowImportStatisticsCommand = new DelegateCommand(ShowImportStatistics);
                ShowSystemDashboardCommand = new DelegateCommand(ShowSystemDashboard);
                ShowBatchImportCommand = new DelegateCommand(ShowBatchImport);
                ForceRefreshDataCommand = new DelegateCommand(ForceRefreshData);

                System.Diagnostics.Debug.WriteLine("Commands initialized");

                // Load initial data
                LoadData();
                System.Diagnostics.Debug.WriteLine("Data loading initiated");

                // Generate initial visit number and driver contract
                GenerateVisitNumber();
                GenerateDriverContract();
                System.Diagnostics.Debug.WriteLine("Visit number and code generated");

                // Set initial hijri date
                UpdateHijriDate();
                System.Diagnostics.Debug.WriteLine("Hijri date updated");

                // Calculate initial days
                CalculateDays();
                System.Diagnostics.Debug.WriteLine("Days calculated");

                // Initialize visitor inputs
                UpdateVisitorInputs();
                System.Diagnostics.Debug.WriteLine("Visitor inputs updated");

                // Start time update timer
                StartTimeUpdateTimer();
                System.Diagnostics.Debug.WriteLine("Timer started");

                // Subscribe to edit visit requests
                EditVisitRequested += OnEditVisitRequested;

                // Update status
                StatusMessage = "������ ���� - ����� ����� ����� �����";
                System.Diagnostics.Debug.WriteLine("DropDataViewModel initialization completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DropDataViewModel constructor: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Initialize minimal required properties to prevent crashes
                FieldVisits = new ObservableCollection<FieldVisit>();
                Sectors = new ObservableCollection<Sector>();
                SectorOfficers = new ObservableCollection<Officer>();
                VisitorInputs = new ObservableCollection<VisitorInput>();

                StatusMessage = $"��� �� �������: {ex.Message}";

                // Initialize basic commands
                AddCommand = new DelegateCommand(() => { }, () => false);
                SaveCommand = new DelegateCommand(() => { }, () => false);
                DeleteCommand = new DelegateCommand(() => { }, () => false);
                IncreaseVisitorsCommand = new DelegateCommand(() => { });
                DecreaseVisitorsCommand = new DelegateCommand(() => { }, () => false);
                ForceRefreshDataCommand = new DelegateCommand(ForceRefreshData);
            }
        }

        #region Properties

        public ObservableCollection<FieldVisit> FieldVisits { get; }
        public ObservableCollection<Sector> Sectors { get; }
        public ObservableCollection<Officer> SectorOfficers { get; }
        public ObservableCollection<VisitorInput> VisitorInputs { get; }
        public ObservableCollection<ItineraryDay> ItineraryDays { get; }
        public ObservableCollection<Project> Projects { get; }
        public ObservableCollection<ProjectInput> ProjectInputs { get; }

        public FieldVisit? SelectedFieldVisit
        {
            get => _selectedFieldVisit;
            set
            {
                SetProperty(ref _selectedFieldVisit, value);
                _ = LoadFieldVisitDetailsAsync(); // ������� async
                RaiseCanExecuteChanged();
            }
        }

        public string NumericInput
        {
            get => _numericInput;
            set
            {
                SetProperty(ref _numericInput, value);
                RaiseCanExecuteChanged();
            }
        }

        public string VisitNumber
        {
            get => _visitNumber;
            set
            {
                SetProperty(ref _visitNumber, value);
                CheckVisitNumberDuplicate();
                RaiseCanExecuteChanged(); // ����� ���� �������
            }
        }

        public string DriverContract
        {
            get => _driverContract;
            set => SetProperty(ref _driverContract, value);
        }

        public bool IsVisitNumberDuplicate
        {
            get => _isVisitNumberDuplicate;
            set => SetProperty(ref _isVisitNumberDuplicate, value);
        }

        public DateTime AddDate
        {
            get => _addDate;
            set
            {
                SetProperty(ref _addDate, value);
                UpdateHijriDate();
            }
        }

        public string HijriDate
        {
            get => _hijriDate;
            set => SetProperty(ref _hijriDate, value);
        }

        public DateTime DepartureDate
        {
            get => _departureDate;
            set
            {
                if (SetProperty(ref _departureDate, value))
                {
                    System.Diagnostics.Debug.WriteLine($"?? �� ����� ����� ������ ���: {value:dd/MM/yyyy}");
                    CalculateDays();
                    RaisePropertyChanged(nameof(DaysCount)); // ����� ����� ����
                }
            }
        }

        public DateTime ReturnDate
        {
            get => _returnDate;
            set
            {
                if (SetProperty(ref _returnDate, value))
                {
                    System.Diagnostics.Debug.WriteLine($"?? �� ����� ����� ������ ���: {value:dd/MM/yyyy}");
                    CalculateDays();
                    RaisePropertyChanged(nameof(DaysCount)); // ����� ����� ����
                }
            }
        }

        public int DaysCount
        {
            get => _daysCount;
            set => SetProperty(ref _daysCount, value);
        }

        public string MissionPurpose
        {
            get => _missionPurpose;
            set
            {
                SetProperty(ref _missionPurpose, value);
                RaiseCanExecuteChanged(); // ����� ���� �������
            }
        }

        public Sector? SelectedSector
        {
            get => _selectedSector;
            set
            {
                SetProperty(ref _selectedSector, value);
                LoadSectorOfficers();
                RaiseCanExecuteChanged();
            }
        }

        public int VisitorsCount
        {
            get => _visitorsCount;
            set
            {
                SetProperty(ref _visitorsCount, value);
                UpdateVisitorInputs();
                RaiseCanExecuteChanged(); // ����� ���� �������
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string SaveButtonText
        {
            get => _saveButtonText;
            set => SetProperty(ref _saveButtonText, value);
        }

        public string CurrentDate
        {
            get => _currentDate;
            set => SetProperty(ref _currentDate, value);
        }

        public string CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        public int TotalParticipants
        {
            get => FieldVisits.Sum(v => v.VisitorsCount);
        }

        public int ActiveSectorsCount
        {
            get => FieldVisits.Select(v => v.SectorId).Distinct().Count();
        }

        public int ProjectsCount
        {
            get => _projectsCount;
            set
            {
                SetProperty(ref _projectsCount, value);
                UpdateProjectInputs(); // ����� ���� �������� ��� ����� �����
                ValidateProjectDays(); // ������ �� ��� ���� ��������
            }
        }

        public string SecurityRoute
        {
            get => _securityRoute;
            set => SetProperty(ref _securityRoute, value);
        }

        public string VisitNotes
        {
            get => _visitNotes;
            set => SetProperty(ref _visitNotes, value);
        }

        // ������ ������� �� Excel (�����)
        private string _approvalBy = string.Empty;
        public string ApprovalBy
        {
            get => _approvalBy;
            set => SetProperty(ref _approvalBy, value);
        }

        private DateTime? _submissionTime;
        public DateTime? SubmissionTime
        {
            get => _submissionTime;
            set
            {
                if (SetProperty(ref _submissionTime, value))
                {
                    RaisePropertyChanged(nameof(FormattedSubmissionTime));
                }
            }
        }

        // ����� ����� ������ ���� �������
        public string FormattedSubmissionTime
        {
            get => SubmissionTime?.ToString("yyyy/MM/dd - HH:mm:ss") ?? "�� ��� ����� ��� �������";
            set { } // setter ���� ����� ����� �����
        }

        // ��� ����� �������
        private string _visitFormNumber = string.Empty;
        public string VisitFormNumber
        {
            get => _visitFormNumber;
            set => SetProperty(ref _visitFormNumber, value);
        }

        // ��� ������� ODK
        private string _odkVisitNumber = string.Empty;
        public string OdkVisitNumber
        {
            get => _odkVisitNumber;
            set
            {
                if (SetProperty(ref _odkVisitNumber, value))
                {
                    RaisePropertyChanged(nameof(ShouldShowApprovalQuestion));
                }
            }
        }

        // ����� ������ �� ��� ��� ��� ����� ���� ��������
        public bool ShouldShowApprovalQuestion => string.IsNullOrWhiteSpace(OdkVisitNumber);

        #endregion

        #region Commands

        public DelegateCommand AddCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand DeleteCommand { get; }
        public DelegateCommand IncreaseVisitorsCommand { get; }
        public DelegateCommand DecreaseVisitorsCommand { get; }
        public DelegateCommand ClearAllDataCommand { get; }
        public DelegateCommand IncreaseProjectsCommand { get; }
        public DelegateCommand DecreaseProjectsCommand { get; }
        public DelegateCommand AddNewProjectCommand { get; }
        public DelegateCommand<ProjectInput> SearchProjectCommand { get; }
        public DelegateCommand IncreaseItineraryCommand { get; }
        public DelegateCommand DecreaseItineraryCommand { get; }
        public DelegateCommand<string> SetApprovalCommand { get; }
        public DelegateCommand ImportExcelCommand { get; }
        public DelegateCommand ShowImportStatisticsCommand { get; }
        public DelegateCommand ShowSystemDashboardCommand { get; }
        public DelegateCommand ShowBatchImportCommand { get; }
        public DelegateCommand ForceRefreshDataCommand { get; }


        #endregion

        #region Command Implementations

        private async void AddFieldVisit()
        {
            // ��� ������� ��� �������
            if (IsVisitNumberDuplicate)
            {
                MessageBox.Show($"? ��� ������� '{VisitNumber}' ����� ������!\n\n���� ������ ��� ����� �����.", "��� ����", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            // ������ �� �������� ��������
            if (string.IsNullOrWhiteSpace(VisitNumber) || string.IsNullOrWhiteSpace(MissionPurpose) || SelectedSector == null)
            {
                MessageBox.Show("���� ����� ���� �������� ��������:\n� ��� �������\n� ���� ������\n� ������", "������ �����", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // ������ �� ��� ������ ��������
            var projectValidation = ValidateProjectsData();
            if (!projectValidation.IsValid)
            {
                MessageBox.Show($"? ��� �� ������ ��������:\n\n{projectValidation.ErrorMessage}", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }






            try
            {
                // ����� ���� ��� ������ ��� ����� ����� �����
                CalculateDays();

                // ����� ���� ��� ����� ��� �����
                System.Diagnostics.Debug.WriteLine("?? === ����� �� ����� ��� ����� ===");
                System.Diagnostics.Debug.WriteLine($"?? ��� ������: {DaysCount}");
                System.Diagnostics.Debug.WriteLine($"?? ��� ����� ItineraryDays: {ItineraryDays.Count}");

                for (int i = 0; i < ItineraryDays.Count; i++)
                {
                    var day = ItineraryDays[i];
                    System.Diagnostics.Debug.WriteLine($"?? ����� {day.DayNumber}: '{day.Itinerary}' (����: {string.IsNullOrWhiteSpace(day.Itinerary)})");
                }

                var itineraryList = ItineraryDays.Select(d => d.Itinerary).ToList();
                System.Diagnostics.Debug.WriteLine($"?? ����� �� ����� ��������: {itineraryList.Count} �����");
                for (int i = 0; i < itineraryList.Count; i++)
                {
                    System.Diagnostics.Debug.WriteLine($"?? ������ {i + 1}: '{itineraryList[i]}'");
                }
                System.Diagnostics.Debug.WriteLine("?? === ����� ������� ===");

                var newVisit = new FieldVisit
                {
                    VisitNumber = VisitNumber,
                    DriverContract = DriverContract,
                    AddDate = AddDate,
                    HijriDate = HijriDate,
                    DepartureDate = DepartureDate,
                    ReturnDate = ReturnDate,
                    DaysCount = DaysCount,
                    MissionPurpose = MissionPurpose,
                    SectorId = SelectedSector?.Id ?? 0,
                    SectorName = SelectedSector?.Name ?? "",
                    VisitorsCount = VisitorsCount,
                    Visitors = VisitorInputs.Where(v => v.SelectedOfficer != null)
                                           .Select(v => new FieldVisitor
                    {
                        Name = v.SelectedOfficer?.Name ?? "",
                        OfficerId = v.SelectedOfficer?.Id ?? 0,
                        OfficerName = v.SelectedOfficer?.Name ?? "",
                        OfficerRank = v.SelectedOfficer?.Rank ?? "",
                        OfficerCode = v.SelectedOfficer?.Code ?? ""
                    }).ToList(),
                    Itinerary = itineraryList,
                    ProjectsCount = ProjectsCount,
                    Projects = await CreateFieldVisitProjectsAsync(ProjectInputs),
                    SecurityRoute = SecurityRoute,
                    VisitNotes = VisitNotes,

                    // ������ ������� �� Excel
                    ApprovalBy = ApprovalBy,
                    SubmissionTime = SubmissionTime,
                    OdkVisitNumber = OdkVisitNumber
                };

                // ����� ������ ������� ��� �����
                System.Diagnostics.Debug.WriteLine($"?? ������ ������� ��� �����:");
                System.Diagnostics.Debug.WriteLine($"   - ApprovalBy: '{newVisit.ApprovalBy}'");
                System.Diagnostics.Debug.WriteLine($"   - SubmissionTime: '{newVisit.SubmissionTime}'");
                System.Diagnostics.Debug.WriteLine($"   - OdkVisitNumber: '{newVisit.OdkVisitNumber}'");

                // ����� ����� ������� �������
                System.Diagnostics.Debug.WriteLine($"?? ������� ������� - �� �����: {newVisit.Itinerary.Count} �����");
                for (int i = 0; i < newVisit.Itinerary.Count; i++)
                {
                    System.Diagnostics.Debug.WriteLine($"?? �� ����� ����� {i + 1}: '{newVisit.Itinerary[i]}'");
                }

                System.Diagnostics.Debug.WriteLine($"?? �������� �������: {newVisit.Projects.Count} �����");
                for (int i = 0; i < newVisit.Projects.Count; i++)
                {
                    var project = newVisit.Projects[i];
                    System.Diagnostics.Debug.WriteLine($"?? ������� {i + 1}: ���='{project.ProjectNumber}', ���='{project.ProjectName}'");
                }

                // ����� �� �� �����
                SaveButtonText = "?? ���� �����...";
                StatusMessage = "?? ���� ��� ��������...";

                var (success, errors) = await _dataService.AddFieldVisitAsync(newVisit);

                if (success)
                {
                    FieldVisits.Add(newVisit);
                    ClearForm();
                    GenerateVisitNumber();
                    GenerateDriverContract();
                    UpdateStatistics();
                    StatusMessage = $"? �� ����� ������� {newVisit.VisitNumber} �����";

                    // ����� ��� �������� ��������
                    NotifyFieldVisitsLogToRefresh();

                    // ����� DashboardViewModel ������ ����� �����
                    FieldVisitAdded?.Invoke();

                    // ����� ����� ���� ����
                    var successMessage = $"?? �� ��� ������� �����!\n\n" +
                                       $"?? ��� �������: {newVisit.VisitNumber}\n" +
                                       $"?? ������: {newVisit.SectorName}\n" +
                                       $"?? ��� ���������: {newVisit.VisitorsCount}\n" +
                                       $"?? ��� ��������: {newVisit.Projects?.Count ?? 0}\n" +
                                       $"?? ����� �������: {newVisit.AddDate:yyyy/MM/dd - HH:mm}\n\n" +
                                       $"? �� ��� ���� �������� ����� �� ������!";

                    MessageBox.Show(successMessage, "?? ��� �����", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    ShowValidationErrors(errors);
                }

                // ����� ����� �� �� �����
                SaveButtonText = "?? ��� �������";
            }
            catch (Exception ex)
            {
                var errorMessage = $"? ��� ��� ��� ����� ����� ����� �������:\n\n";
                errorMessage += $"��� �����: {ex.GetType().Name}\n";
                errorMessage += $"����� �����: {ex.Message}\n";

                if (ex.InnerException != null)
                {
                    errorMessage += $"������ ������: {ex.InnerException.Message}\n";
                }

                errorMessage += "\n���� �������� ��� ���� �� ������� ������ �����";
                MessageBox.Show(errorMessage, "��� ��� �����", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowValidationErrors(List<string> errors)
        {
            if (!errors.Any())
                return;

            var errorMessage = "? �� ������ ��� ������� �������:\n\n";

            for (int i = 0; i < errors.Count; i++)
            {
                errorMessage += $"{i + 1}. {errors[i]}\n";
            }

            errorMessage += "\n?? ���� ����� ��� ������� ������ ��������";

            MessageBox.Show(errorMessage,
                           $"����� �� �������� ({errors.Count} ���)",
                           MessageBoxButton.OK,
                           MessageBoxImage.Warning);
        }

        private async void SaveOrUpdateFieldVisit()
        {
            if (SelectedFieldVisit != null)
            {
                // ����� ����� ������
                await UpdateFieldVisit();
            }
            else
            {
                // ����� ����� �����
                AddFieldVisit();
            }
        }

        private async void SaveFieldVisit()
        {
            if (SelectedFieldVisit == null)
            {
                System.Diagnostics.Debug.WriteLine("? SaveFieldVisit: SelectedFieldVisit is null");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine($"?? SaveFieldVisit: ��� ��� ������� ������� {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");

                // ������ �� ��� �������� �������� ��� �����
                if (string.IsNullOrWhiteSpace(VisitNumber))
                {
                    System.Diagnostics.Debug.WriteLine("? ��� ������� ����");
                    MessageBox.Show("? ��� ������� �����", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // ������ �� ���� ��� ������� �� ������
                await CheckVisitNumberExistenceAsync();

                if (SelectedSector == null)
                {
                    System.Diagnostics.Debug.WriteLine("? �� ��� ������ ����");
                    MessageBox.Show("? ��� ������ ���� �������", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(MissionPurpose))
                {
                    System.Diagnostics.Debug.WriteLine("? ���� ������ �����");
                    MessageBox.Show("? ���� ������ ������", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // ����� ��������
                SelectedFieldVisit.AddDate = AddDate;
                SelectedFieldVisit.HijriDate = HijriDate;
                SelectedFieldVisit.DepartureDate = DepartureDate;
                SelectedFieldVisit.ReturnDate = ReturnDate;
                SelectedFieldVisit.DaysCount = DaysCount;
                SelectedFieldVisit.MissionPurpose = MissionPurpose;
                SelectedFieldVisit.SectorId = SelectedSector.Id;
                SelectedFieldVisit.SectorName = SelectedSector.Name;
                SelectedFieldVisit.VisitorsCount = VisitorsCount;
                SelectedFieldVisit.Visitors = VisitorInputs.Where(v => v.SelectedOfficer != null)
                                                          .Select(v => new FieldVisitor
                {
                    Name = v.SelectedOfficer?.Name ?? "",
                    OfficerId = v.SelectedOfficer?.Id ?? 0,
                    OfficerName = v.SelectedOfficer?.Name ?? "",
                    OfficerRank = v.SelectedOfficer?.Rank ?? "",
                    OfficerCode = v.SelectedOfficer?.Code ?? ""
                }).ToList();

                System.Diagnostics.Debug.WriteLine($"?? ������ ��� ������� �������: {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");

                var success = await _dataService.UpdateFieldVisitAsync(SelectedFieldVisit);
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"? �� ��� ��������� ����� �������: {SelectedFieldVisit.VisitNumber}");
                    UpdateStatistics();
                    StatusMessage = $"�� ����� ������� {SelectedFieldVisit.VisitNumber} �����";
                    MessageBox.Show("? �� ��� ��������� �����", "��� �������", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"? ��� �� ��� ��������� �������: {SelectedFieldVisit.VisitNumber}");

                    // ����� ������� �������� ��� ��� �����
                    System.Diagnostics.Debug.WriteLine("?? ��� ������� ��������...");

                    try
                    {
                        // ����� DatabaseService �������
                        var sqliteService = new Services.DatabaseService();
                        var diagnosticHelper = new Helpers.FieldVisitDiagnosticHelper(sqliteService);
                        var diagnosticResult = await diagnosticHelper.DiagnoseFieldVisitUpdateAsync(SelectedFieldVisit);

                        // ���� ����� ��� ����� �� ����� �������
                        var errorMessage = "? ��� �� ��� ���������\n\n";

                        if (diagnosticResult.Errors.Any())
                        {
                            errorMessage += "?? ������� ��������:\n";
                            foreach (var error in diagnosticResult.Errors)
                            {
                                errorMessage += $"� {error}\n";
                            }
                            errorMessage += "\n";
                        }

                        if (diagnosticResult.Warnings.Any())
                        {
                            errorMessage += "?? �������:\n";
                            foreach (var warning in diagnosticResult.Warnings)
                            {
                                errorMessage += $"� {warning}\n";
                            }
                            errorMessage += "\n";
                        }

                        errorMessage += "?? ������ ��������:\n";
                        errorMessage += "� ����� ����� ��������\n";
                        errorMessage += "� ������ �� ������� ����� ��������\n";
                        errorMessage += "� ������ �� ��� �������� �������\n";
                        errorMessage += "� ������� ������ ����� ��� ������ �������";

                        MessageBox.Show(errorMessage, "��� �� ����� - ����� ������", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    catch (Exception diagEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"? ��� �� ������� ��������: {diagEx.Message}");

                        var errorMessage = "? ��� �� ��� ���������\n\n";
                        errorMessage += "������� ��������:\n";
                        errorMessage += "� ��� �� ����� ����� ��������\n";
                        errorMessage += "� ����� �� ��������\n";
                        errorMessage += "� ����� �� ������� �����\n\n";
                        errorMessage += "?? ���� �������� ��� ���� �� ����� ����� ��������";

                        MessageBox.Show(errorMessage, "��� �� �����", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ������� �� ��� ���������: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"? ������ �����: {ex.StackTrace}");

                var errorMessage = $"? ��� �� ��� ���������:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\n������ ������:\n{ex.InnerException.Message}";
                }
                errorMessage += "\n\n?? ���� ������ �� ����� ����� �������� ��������� ��� ����";

                MessageBox.Show(errorMessage, "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task UpdateFieldVisit()
        {
            if (SelectedFieldVisit == null)
            {
                System.Diagnostics.Debug.WriteLine("? UpdateFieldVisit: SelectedFieldVisit is null");
                return;
            }

            try
            {
                System.Diagnostics.Debug.WriteLine($"?? UpdateFieldVisit: ��� ����� ������� {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");

                // ������ �� ��� �������� �������� ��� �������
                if (string.IsNullOrWhiteSpace(VisitNumber))
                {
                    System.Diagnostics.Debug.WriteLine("? ��� ������� ����");
                    MessageBox.Show("? ��� ������� �����", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // ������ �� ���� ��� ������� �� ������
                await CheckVisitNumberExistenceAsync();

                if (SelectedSector == null)
                {
                    System.Diagnostics.Debug.WriteLine("? �� ��� ������ ����");
                    MessageBox.Show("? ��� ������ ���� �������", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (string.IsNullOrWhiteSpace(MissionPurpose))
                {
                    System.Diagnostics.Debug.WriteLine("? ���� ������ �����");
                    MessageBox.Show("? ���� ������ ������", "��� �� ��������", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // ����� ���� ��� ������ ��� ����� ����� �����
                CalculateDays();

                // ����� �������� ��������
                SelectedFieldVisit.VisitNumber = VisitNumber;
                SelectedFieldVisit.AddDate = AddDate;
                SelectedFieldVisit.HijriDate = HijriDate;
                SelectedFieldVisit.DepartureDate = DepartureDate;
                SelectedFieldVisit.ReturnDate = ReturnDate;
                SelectedFieldVisit.DaysCount = DaysCount;
                SelectedFieldVisit.MissionPurpose = MissionPurpose;
                SelectedFieldVisit.SectorId = SelectedSector.Id;
                SelectedFieldVisit.SectorName = SelectedSector.Name;
                SelectedFieldVisit.VisitorsCount = VisitorsCount;

                // ����� ������
                SelectedFieldVisit.Visitors = VisitorInputs.Where(v => v.SelectedOfficer != null)
                                                          .Select(v => new FieldVisitor
                {
                    Name = v.SelectedOfficer?.Name ?? "",
                    OfficerId = v.SelectedOfficer?.Id ?? 0,
                    OfficerName = v.SelectedOfficer?.Name ?? "",
                    OfficerRank = v.SelectedOfficer?.Rank ?? "",
                    OfficerCode = v.SelectedOfficer?.Code ?? ""
                }).ToList();

                // ����� �� �����
                SelectedFieldVisit.Itinerary = ItineraryDays.Select(d => d.Itinerary ?? "").ToList();

                // ����� ��������
                SelectedFieldVisit.ProjectsCount = ProjectsCount;
                SelectedFieldVisit.Projects = await CreateFieldVisitProjectsAsync(ProjectInputs);

                // ����� ������ ��������
                SelectedFieldVisit.SecurityRoute = SecurityRoute;
                SelectedFieldVisit.VisitNotes = VisitNotes;

                // ����� ������ ������� �� Excel
                SelectedFieldVisit.OdkVisitNumber = OdkVisitNumber;
                SelectedFieldVisit.ApprovalBy = ApprovalBy;
                SelectedFieldVisit.SubmissionTime = SubmissionTime;

                // ����� ������ ������� ��� �������
                System.Diagnostics.Debug.WriteLine($"?? ������ ������� ��� �������:");
                System.Diagnostics.Debug.WriteLine($"   - ApprovalBy: '{SelectedFieldVisit.ApprovalBy}'");
                System.Diagnostics.Debug.WriteLine($"   - SubmissionTime: '{SelectedFieldVisit.SubmissionTime}'");
                System.Diagnostics.Debug.WriteLine($"   - OdkVisitNumber: '{SelectedFieldVisit.OdkVisitNumber}'");

                System.Diagnostics.Debug.WriteLine($"?? ������ ����� �������: {SelectedFieldVisit.VisitNumber} (ID: {SelectedFieldVisit.Id})");
                System.Diagnostics.Debug.WriteLine($"?? ������ ������� �������:");
                System.Diagnostics.Debug.WriteLine($"   - ������: {SelectedFieldVisit.SectorName} (ID: {SelectedFieldVisit.SectorId})");
                System.Diagnostics.Debug.WriteLine($"   - ��� ������: {SelectedFieldVisit.VisitorsCount}");
                System.Diagnostics.Debug.WriteLine($"   - ��� ��������: {SelectedFieldVisit.ProjectsCount}");
                System.Diagnostics.Debug.WriteLine($"   - ��� ����� �� �����: {SelectedFieldVisit.Itinerary.Count}");

                var success = await _dataService.UpdateFieldVisitAsync(SelectedFieldVisit);
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"? �� ����� ������� �����: {SelectedFieldVisit.VisitNumber}");
                    UpdateStatistics();
                    StatusMessage = $"�� ����� ������� {SelectedFieldVisit.VisitNumber} �����";

                    // ����� ��� �������� ��������
                    NotifyFieldVisitsLogToRefresh();

                    // ����� DashboardViewModel ������ �����
                    FieldVisitAdded?.Invoke();

                    MessageBox.Show("? �� ��� ��������� �����", "��� �������", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"? ��� �� ����� �������: {SelectedFieldVisit.VisitNumber}");

                    // ����� ������� �������� ��� ��� �������
                    System.Diagnostics.Debug.WriteLine("?? ��� ������� ��������...");

                    try
                    {
                        // ����� DatabaseService �������
                        var sqliteService = new Services.DatabaseService();
                        var diagnosticHelper = new Helpers.FieldVisitDiagnosticHelper(sqliteService);
                        var diagnosticResult = await diagnosticHelper.DiagnoseFieldVisitUpdateAsync(SelectedFieldVisit);

                        // ���� ����� ��� ����� �� ����� �������
                        var errorMessage = "? ��� �� ��� ���������\n\n";

                        if (diagnosticResult.Errors.Any())
                        {
                            errorMessage += "?? ������� ��������:\n";
                            foreach (var error in diagnosticResult.Errors)
                            {
                                errorMessage += $"� {error}\n";
                            }
                            errorMessage += "\n";
                        }

                        if (diagnosticResult.Warnings.Any())
                        {
                            errorMessage += "?? �������:\n";
                            foreach (var warning in diagnosticResult.Warnings)
                            {
                                errorMessage += $"� {warning}\n";
                            }
                            errorMessage += "\n";
                        }

                        errorMessage += "?? ������ ��������:\n";
                        errorMessage += "� ����� ����� ��������\n";
                        errorMessage += "� ������ �� ������� ����� ��������\n";
                        errorMessage += "� ������ �� ��� �������� �������\n";
                        errorMessage += "� ������� ������ ����� ��� ������ �������";

                        MessageBox.Show(errorMessage, "��� �� ����� - ����� ������", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    catch (Exception diagEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"? ��� �� ������� ��������: {diagEx.Message}");

                        var errorMessage = "? ��� �� ��� ���������\n\n";
                        errorMessage += "������� ��������:\n";
                        errorMessage += "� ��� �� ����� ����� ��������\n";
                        errorMessage += "� ����� �� ��������\n";
                        errorMessage += "� ����� �� ������� �����\n";
                        errorMessage += "� ��� �� ��� �������� �� �� �����\n\n";
                        errorMessage += "?? ���� �������� ��� ���� �� ����� ����� ��������";

                        MessageBox.Show(errorMessage, "��� �� �����", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ������� �� ����� �������: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"? ������ �����: {ex.StackTrace}");

                var errorMessage = $"? ��� �� ��� ���������:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\n������ ������:\n{ex.InnerException.Message}";
                }
                errorMessage += "\n\n?? ���� ������ �� ����� ����� �������� ��������� ��� ����";

                MessageBox.Show(errorMessage, "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteFieldVisit()
        {
            if (SelectedFieldVisit == null) return;

            var result = MessageBox.Show($"�� ��� ����� �� ��� ������� ��� '{SelectedFieldVisit.VisitNumber}'�",
                                       "����� �����", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var visitToDelete = SelectedFieldVisit;
                    var success = await _dataService.DeleteFieldVisitAsync(visitToDelete.Id);
                    if (success)
                    {
                        var deletedVisitNumber = visitToDelete.VisitNumber;

                        // ����� �� ������� �������
                        FieldVisits.Remove(visitToDelete);

                        // ��� �������
                        ClearForm();
                        GenerateVisitNumber();

                        // ����� ����� �������� ������ �� ����� �������
                        await RefreshDataAsync();

                        UpdateStatistics();
                        StatusMessage = $"�� ��� ������� {deletedVisitNumber} ����� ��������";
                    }
                    else
                    {
                        MessageBox.Show("? ��� �� ��� ������� ���������", "���", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"? ��� �� ��� �������: {ex.Message}", "���", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private bool CanExecuteAdd()
        {
            var canAdd = !string.IsNullOrWhiteSpace(VisitNumber) &&
                        !IsVisitNumberDuplicate &&
                        !string.IsNullOrWhiteSpace(MissionPurpose) &&
                        SelectedSector != null &&
                        VisitorsCount > 0;

            System.Diagnostics.Debug.WriteLine($"CanExecuteAdd: {canAdd} - VisitNumber: '{VisitNumber}', IsDuplicate: {IsVisitNumberDuplicate}, MissionPurpose: '{MissionPurpose}', SelectedSector: {SelectedSector?.Name}, VisitorsCount: {VisitorsCount}");

            return canAdd;
        }

        private bool CanExecuteSaveOrUpdate()
        {
            return CanExecuteAdd(); // ���� ����� ���� ��� ����� �� �����
        }

        private bool CanExecuteSave()
        {
            return SelectedFieldVisit != null && CanExecuteAdd();
        }

        private bool CanExecuteDelete()
        {
            return SelectedFieldVisit != null;
        }

        private void IncreaseVisitors()
        {
            if (VisitorsCount < 20) // �� ���� 20 �����
            {
                VisitorsCount++;
            }
        }

        private void DecreaseVisitors()
        {
            if (VisitorsCount > 1)
            {
                VisitorsCount--;
            }
        }

        private bool CanDecreaseVisitors()
        {
            return VisitorsCount > 1;
        }

        private async void ClearAllData()
        {
            var result = MessageBox.Show(
                "�� ��� ����� �� ����� ���� ������ �������� ��������ɿ",
                "����� ����� ��������",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var success = await _dataService.ClearAllFieldVisitsAsync();
                    if (success)
                    {
                        FieldVisits.Clear();
                        ClearForm();
                        GenerateVisitNumber();
                        UpdateStatistics();
                        StatusMessage = "? �� ����� ���� ������ �������� ��������� �����";
                    }
                    else
                    {
                        MessageBox.Show("? ��� �� ����� ��������", "���", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"? ��� �� ����� ��������: {ex.Message}", "���", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }



        private void IncreaseProjects()
        {
            ProjectsCount++;
        }

        private void DecreaseProjects()
        {
            if (ProjectsCount > 0)
            {
                ProjectsCount--;
            }
        }

        private bool CanDecreaseProjects()
        {
            return ProjectsCount > 0;
        }

        private void IncreaseItinerary()
        {
            var newDay = new ItineraryDay
            {
                DayNumber = ItineraryDays.Count + 1,
                Itinerary = string.Empty
            };
            ItineraryDays.Add(newDay);
            System.Diagnostics.Debug.WriteLine($"??? �� ����� ��� ���� ��� ����� - ����� ������: {ItineraryDays.Count}");
        }

        private void DecreaseItinerary()
        {
            if (ItineraryDays.Count > 1)
            {
                ItineraryDays.RemoveAt(ItineraryDays.Count - 1);
                System.Diagnostics.Debug.WriteLine($"??? �� ��� ��� �� �� ����� - ����� ������: {ItineraryDays.Count}");
            }
        }

        private bool CanDecreaseItinerary()
        {
            return ItineraryDays.Count > 1;
        }

        private async void ImportExcelFile()
        {
            var startTime = DateTime.Now;
            var importLogService = new Services.ImportLogService();
            string selectedFileName = "";

            try
            {
                // ������ �� ���� ��� �����
                if (string.IsNullOrWhiteSpace(NumericInput))
                {
                    MessageBox.Show("?? ��� ����� ����� ������� ����� ��� ������� ��� Excel",
                                  "��� ����� �����", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                StatusMessage = "���� ��� ���� ���� ������ �����...";

                // ������� ���� ��� ��� ����
                var folderMemoryService = new Services.FolderMemoryService();
                var lastFolder = folderMemoryService.GetLastFolder();

                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "���� ��� Excel ���������",
                    Filter = "����� Excel (*.xlsx;*.xls)|*.xlsx;*.xls|���� ������� (*.*)|*.*",
                    FilterIndex = 1,
                    InitialDirectory = lastFolder,
                    RestoreDirectory = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    selectedFileName = System.IO.Path.GetFileName(openFileDialog.FileName);
                    StatusMessage = "���� ������� �������� �� ��� Excel...";

                    // ��� ��� ����
                    var selectedFolder = folderMemoryService.GetFolderFromPath(openFileDialog.FileName);
                    folderMemoryService.SaveLastFolder(selectedFolder);

                    // ������� ���� ������� Excel �������
                    var excelImportService = new Services.ExcelImportService();
                    var importResult = await excelImportService.ImportFieldVisitFromExcel(openFileDialog.FileName, NumericInput);

                    if (importResult.VisitData != null)
                    {
                        // ��� ����� ������ �� ��� �������� �����
                        if (importResult.ValidationResult != null)
                        {
                            var validationWindow = new Views.ValidationResultWindow(importResult.ValidationResult);
                            var validationResult = validationWindow.ShowDialog();

                            // ������ �� ����� ��������
                            if (validationResult != true ||
                                (validationWindow.UserAction != Views.ValidationAction.Continue &&
                                 validationWindow.UserAction != Views.ValidationAction.AcceptWithErrors))
                            {
                                StatusMessage = "�� ����� ��������� ���� ����� �� ��������";
                                return;
                            }

                            // ��� ����� �������� ������ �� �������� ��� ����� �����
                            if (validationWindow.UserAction == Views.ValidationAction.AcceptWithErrors)
                            {
                                var confirmMessage = "?? ����� ������ �� �������\n\n";
                                confirmMessage += "��� ��� ��� ���� �������� ��� ���� �����.\n";
                                confirmMessage += "��� �� ���� ��� ���� �������� �� ������.\n\n";
                                confirmMessage += "�� ��� ����� �� �������ɿ";

                                var confirmResult = MessageBox.Show(confirmMessage, "����� ������",
                                    MessageBoxButton.YesNo, MessageBoxImage.Warning);

                                if (confirmResult == MessageBoxResult.No)
                                {
                                    StatusMessage = "�� ����� ���������";
                                    return;
                                }

                                System.Diagnostics.Debug.WriteLine("? �������� ��� ���� �������� �� �������");
                            }
                        }

                        // ��� ��� �����ޡ ��� ����� ��������
                        var previewWindow = new Views.ExcelPreviewWindow(importResult);
                        var previewResult = previewWindow.ShowDialog();

                        if (previewResult == true && previewWindow.IsConfirmed)
                        {
                            // �������� ��� ���������
                            await PopulateFormFromImportData(importResult);

                            // ����� ����� ��������� �������
                            var processingTime = DateTime.Now - startTime;
                            var logEntry = new Services.ImportLogEntry
                            {
                                FileName = selectedFileName,
                                IndexValue = NumericInput,
                                VisitNumber = importResult.VisitData.VisitFormNumber,
                                ProjectsCount = importResult.Projects.Count,
                                ItineraryDaysCount = importResult.Itinerary.Count,
                                ProcessingTime = processingTime
                            };
                            await importLogService.LogSuccessfulImportAsync(logEntry);

                            MessageBox.Show($"? �� ������� �������� ����� �� �����:\n{selectedFileName}\n\n" +
                                          $"?? �������� ���������:\n" +
                                          $"� ��� �������: {importResult.VisitData.VisitFormNumber}\n" +
                                          $"� ��� ������: {importResult.VisitData.FieldDaysCount}\n" +
                                          $"� ��������: {importResult.Projects.Count}\n" +
                                          $"� �� �����: {importResult.Itinerary.Count} ����\n" +
                                          $"?? ��� ��������: {processingTime.TotalSeconds:F1} �����",
                                          "��� ���������", MessageBoxButton.OK, MessageBoxImage.Information);

                            StatusMessage = "? �� ������� �������� �� Excel ������ ������ �����";
                        }
                        else
                        {
                            StatusMessage = "�� ����� ������� �������� �� ��� ��������";
                        }
                    }
                    else
                    {
                        // ����� ����� ��������� �������
                        await importLogService.LogFailedImportAsync(selectedFileName, importResult.ErrorMessage, NumericInput);

                        MessageBox.Show($"? ��� �� ������� ��������:\n{importResult.ErrorMessage}",
                                      "��� �� ���������", MessageBoxButton.OK, MessageBoxImage.Error);
                        StatusMessage = $"? ��� �� ���������: {importResult.ErrorMessage}";
                    }
                }
                else
                {
                    StatusMessage = "�� ����� ����� ���������";
                }
            }
            catch (Exception ex)
            {
                // ����� ����� �����
                await importLogService.LogFailedImportAsync(selectedFileName, ex.Message, NumericInput);

                MessageBox.Show($"? ��� �� ������� ��� Excel:\n{ex.Message}",
                              "��� �� ���������", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusMessage = "? ��� �� ������� ��� Excel";
            }
        }

        /// <summary>
        /// ��� ����� �������� ���������
        /// </summary>
        private void ShowImportStatistics()
        {
            try
            {
                var statisticsWindow = new Views.ImportStatisticsWindow();
                statisticsWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine("? �� ��� ����� �������� ���������");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ��� ����� ����������: {ex.Message}");
                MessageBox.Show($"��� �� ��� ����� ����������:\n{ex.Message}",
                              "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ��� ���� ������ ��������
        /// </summary>
        private void ShowSystemDashboard()
        {
            try
            {
                var dashboardWindow = new Views.SystemDashboardWindow();
                dashboardWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine("?? �� ��� ���� ������ ��������");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ��� ���� ������: {ex.Message}");
                MessageBox.Show($"��� �� ��� ���� ������:\n{ex.Message}",
                              "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ��� ����� ��������� �������
        /// </summary>
        private void ShowBatchImport()
        {
            try
            {
                var batchImportWindow = new Views.BatchImportWindow();
                batchImportWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine("?? �� ��� ����� ��������� �������");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ��� ����� ��������� �������: {ex.Message}");
                MessageBox.Show($"��� �� ��� ����� ��������� �������:\n{ex.Message}",
                              "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// ����� ������� �� �������� ��������� �� Excel
        /// </summary>
        private async Task PopulateFormFromImportData(Services.FieldVisitImportResult importResult)
        {
            try
            {
                var visitData = importResult.VisitData;
                if (visitData == null) return;

                System.Diagnostics.Debug.WriteLine("?? ��� ����� ������� �� �������� ���������...");

                // ����� �������� ��������
                VisitNumber = visitData.VisitFormNumber;
                DaysCount = visitData.FieldDaysCount;
                MissionPurpose = visitData.TripPurpose;

                // ����� ��������
                if (visitData.StartDate.HasValue)
                    DepartureDate = visitData.StartDate.Value;
                if (visitData.EndDate.HasValue)
                    ReturnDate = visitData.EndDate.Value;

                // ����� �� ������ ������
                if (!string.IsNullOrWhiteSpace(visitData.Sector))
                {
                    var sector = await _dataService.GetSectorByCodeAsync(visitData.Sector);
                    if (sector != null)
                    {
                        SelectedSector = Sectors.FirstOrDefault(s => s.Id == sector.Id);
                        System.Diagnostics.Debug.WriteLine($"? �� ������ ��� ������: {sector.Name}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"?? �� ��� ������ ��� ���� ������: {visitData.Sector}");
                    }
                }

                // ����� �������� ��������
                if (!string.IsNullOrWhiteSpace(visitData.VisitorCodes))
                {
                    // ����� ����� �������� �������� ������ ������� ��������
                    await ProcessVisitorCodesForFormAsync(visitData.VisitorCodes);
                }
                else if (!string.IsNullOrWhiteSpace(visitData.Visitors))
                {
                    // ������ ������� ��� ������ �� ���� (������� �� ����� �������)
                    if (int.TryParse(visitData.Visitors, out int visitorsCount))
                    {
                        VisitorsCount = Math.Max(1, visitorsCount);
                    }
                    else
                    {
                        // ��� ��� ���� ����� ��� ������ ���� ��� �������
                        var names = visitData.Visitors.Split(new[] { " - " }, StringSplitOptions.RemoveEmptyEntries);
                        VisitorsCount = Math.Max(1, names.Length);
                    }
                }

                // ����� ��������
                if (importResult.Projects.Any())
                {
                    ProjectsCount = Math.Min(importResult.Projects.Count, 5); // ���� ������ 5 ������
                    UpdateProjectInputs();

                    for (int i = 0; i < Math.Min(importResult.Projects.Count, ProjectInputs.Count); i++)
                    {
                        var importedProject = importResult.Projects[i];
                        var projectInput = ProjectInputs[i];

                        projectInput.ProjectNumber = importedProject.ProjectCode;
                        projectInput.ProjectName = importedProject.ProjectName;
                        projectInput.ProjectDays = importedProject.ProjectDays;
                        projectInput.IsProjectFound = !string.IsNullOrWhiteSpace(importedProject.ProjectName);
                    }
                    System.Diagnostics.Debug.WriteLine($"? �� ����� {importResult.Projects.Count} �����");
                }

                // ����� �� �����
                if (importResult.Itinerary.Any())
                {
                    // ����� ��� ���� �� �����
                    UpdateItineraryDays();

                    for (int i = 0; i < Math.Min(importResult.Itinerary.Count, ItineraryDays.Count); i++)
                    {
                        ItineraryDays[i].Itinerary = importResult.Itinerary[i].Plan;
                    }
                    System.Diagnostics.Debug.WriteLine($"? �� ����� �� ����� �� {importResult.Itinerary.Count} ����");
                }

                // ����� �� ����� ������ �������
                if (!string.IsNullOrWhiteSpace(visitData.SecurityRoute))
                {
                    SecurityRoute = visitData.SecurityRoute;
                    System.Diagnostics.Debug.WriteLine($"? �� ����� �� ����� ������ �������: {visitData.SecurityRoute}");
                }

                // ����� ������� �������
                if (!string.IsNullOrWhiteSpace(visitData.VisitNotes))
                {
                    VisitNotes = visitData.VisitNotes;
                    System.Diagnostics.Debug.WriteLine($"? �� ����� ������� �������: {visitData.VisitNotes}");
                }

                // ����� ������ ������� (���� �� ������� ������)
                // ����� ��� �������� ��� �� ����
                ApprovalBy = ConvertApprovalCode(visitData.ApprovalBy ?? string.Empty);
                System.Diagnostics.Debug.WriteLine($"? �� ����� �������� ��� �����: '{ApprovalBy}'");

                // ����� ��� �������
                SubmissionTime = visitData.SubmissionTime;
                System.Diagnostics.Debug.WriteLine($"? �� ����� ��� �������: '{FormattedSubmissionTime}'");

                // ����� ��� ������� ODK
                System.Diagnostics.Debug.WriteLine($"?? ��� ������� ODK �� Excel: '{visitData.OdkVisitNumber}'");
                System.Diagnostics.Debug.WriteLine($"?? ��� ����� ������� �� Excel: '{visitData.VisitFormNumber}'");

                OdkVisitNumber = visitData.OdkVisitNumber ?? string.Empty;
                System.Diagnostics.Debug.WriteLine($"? �� ����� ��� ������� ODK: '{OdkVisitNumber}'");

                // ��� ��� ��� ODK ���ۡ ������ ��� ����� �������
                if (string.IsNullOrWhiteSpace(OdkVisitNumber) && !string.IsNullOrWhiteSpace(visitData.VisitFormNumber))
                {
                    OdkVisitNumber = visitData.VisitFormNumber;
                    System.Diagnostics.Debug.WriteLine($"?? ��� ODK ���ۡ ������� ��� ����� �������: '{OdkVisitNumber}'");
                }

                // ����� ������� ������
                UpdateHijriDate();

                System.Diagnostics.Debug.WriteLine("? �� ����� ������� ����� �� �������� ���������");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ����� �������: {ex.Message}");
                MessageBox.Show($"? ��� �� ����� ������� �� �������� ���������:\n{ex.Message}",
                              "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ����� ��� �������� ��� �� ����
        /// </summary>
        private string ConvertApprovalCode(string approvalCode)
        {
            return approvalCode?.ToLower() switch
            {
                "branch_manager" => "���� �����",
                "branch_deputy" => "���� ���� �����",
                "program_officer" => "���� ������� ��������",
                _ => approvalCode ?? "��� ����"
            };
        }

        /// <summary>
        /// ����� �������� ��� �����
        /// </summary>
        private void SetApproval(string approvalCode)
        {
            try
            {
                ApprovalBy = ConvertApprovalCode(approvalCode);
                System.Diagnostics.Debug.WriteLine($"? �� ����� ��������: {ApprovalBy}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ����� ��������: {ex.Message}");
            }
        }

        private async void AddNewProject()
        {
            try
            {
                var addProjectWindow = new Views.AddProjectWindow();

                var result = addProjectWindow.ShowDialog();

                if (result == true)
                {
                    // ����� ����� �������� ��� �������
                    await RefreshProjectsAsync();
                    StatusMessage = "? �� ����� ������� ����� ������ �������";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"? ��� �� ��� ����� ����� �������: {ex.Message}", "���",
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ����� ��� �������� ��������� �������� - ���� �����
        /// </summary>
        private async void NotifyFieldVisitsLogToRefresh()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("?? ��� ����� ��� �������� ��������...");

                // ������� Dispatcher ������ �� ������� �� UI Thread
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    // ����� �� MainWindow
                    var mainWindow = Application.Current.MainWindow as MainWindow;
                    if (mainWindow?.DataContext is MainViewModel mainViewModel)
                    {
                        System.Diagnostics.Debug.WriteLine("? �� ������ ��� MainWindow");

                        // ����� ��� ����� ViewModels ��������
                        FieldVisitsUpdated?.Invoke();

                        // ����� ����� ��� ��� �������� �� ���� ��� ��������
                        if (mainViewModel.CurrentView is Views.FieldVisitsLogView logView &&
                            logView.DataContext is FieldVisitsLogViewModel logViewModel)
                        {
                            System.Diagnostics.Debug.WriteLine("? �������� �� ���� ��� �������� - ����� �����");
                            await Task.Delay(500); // ������ ���� ������ �� ��� ��������
                            logViewModel.RefreshDataCommand.Execute();
                        }

                        System.Diagnostics.Debug.WriteLine("? �� ����� ��� �������� �������� �����");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("?? �� ��� ������ ��� MainWindow �� MainViewModel");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ����� ��� ��������: {ex.Message}");
            }
        }

        /// <summary>
        /// ��� ������ ViewModels ���� ������ ��������
        /// </summary>
        public static event Action? FieldVisitsUpdated;

        /// <summary>
        /// ��� ������ DashboardViewModel ������ ����� �����
        /// </summary>
        public static event Action? FieldVisitAdded;

        /// <summary>
        /// ��� ���� ����� ����� �� ����� ����
        /// </summary>
        public static event Action<FieldVisit>? EditVisitRequested;

        /// <summary>
        /// ����� ���� ���� ����� �����
        /// </summary>
        public static void RequestEditVisit(FieldVisit visit)
        {
            EditVisitRequested?.Invoke(visit);
        }

        /// <summary>
        /// ����� ��� ��� ����� �����
        /// </summary>
        private async void OnEditVisitRequested(FieldVisit visit)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"?? OnEditVisitRequested: �� ������ ��� ����� ������� {visit.VisitNumber}");

                // Use dispatcher to ensure we're on UI thread
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        // Clear any existing data first
                        ClearForm();

                        // Wait a moment for the form to clear
                        await Task.Delay(100);

                        // Set the visit for editing - this will trigger LoadFieldVisitDetailsAsync
                        SelectedFieldVisit = visit;

                        System.Diagnostics.Debug.WriteLine($"? OnEditVisitRequested: �� ����� ������� ������� - {visit.VisitNumber}");

                        // Show success message
                        StatusMessage = $"�� ����� ������� {visit.VisitNumber} �������";

                        // Show confirmation to user
                        MessageBox.Show($"? �� ����� ������� {visit.VisitNumber} ������� �����\n\n����� ���� ����� �������� ������",
                                      "�� �������", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception innerEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"? OnEditVisitRequested Inner Error: {innerEx.Message}");
                        StatusMessage = $"��� �� ����� ������� �������: {innerEx.Message}";
                        MessageBox.Show($"? ��� �� ����� ������� �������:\n{innerEx.Message}", "���", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? OnEditVisitRequested Error: {ex.Message}");
                StatusMessage = $"��� �� ����� ������� �������: {ex.Message}";
                MessageBox.Show($"? ��� �� ����� ������� �������:\n{ex.Message}", "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ������ ����� �������� �������� ������ �������
        /// </summary>
        private async Task ProcessVisitorCodesForFormAsync(string visitorCodes)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(visitorCodes))
                {
                    System.Diagnostics.Debug.WriteLine("?? �� ���� ����� ������ ��������");
                    return;
                }

                // ����� ������� (������ �������)
                var codes = visitorCodes.Split(new[] { ' ', '\t', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                       .Select(c => c.Trim())
                                       .Where(c => !string.IsNullOrEmpty(c))
                                       .ToList();

                System.Diagnostics.Debug.WriteLine($"?? ����� �������� ��������: {string.Join(", ", codes)}");

                if (codes.Count == 0)
                {
                    return;
                }

                // ����� ��� ������
                VisitorsCount = codes.Count;
                UpdateVisitorInputs();

                // ����� �� �������� ��������
                using var context = new ApplicationDbContext();
                var allOfficers = await context.Officers
                                              .Where(o => o.IsActive)
                                              .ToListAsync();

                // تنظيف الأكواد من الأقواس للمقارنة
                var officers = new List<Officer>();
                foreach (var code in codes)
                {
                    var officer = allOfficers.FirstOrDefault(o =>
                        o.Code.Replace("(", "").Replace(")", "").Trim() == code);
                    if (officer != null)
                    {
                        officers.Add(officer);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"?? �� ������ ��� {officers.Count} ���� �� ��� {codes.Count}");

                // ����� ������ ����� �� ��� ���� �����
                var firstOfficer = officers.FirstOrDefault();
                if (firstOfficer != null && SelectedSector == null && firstOfficer.SectorId > 0)
                {
                    SelectedSector = Sectors.FirstOrDefault(s => s.Id == firstOfficer.SectorId);
                    if (SelectedSector != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"?? �� ����� ������: {SelectedSector.Name}");
                        await UpdateSectorOfficersAsync();
                        System.Diagnostics.Debug.WriteLine($"?? �� ����� ����� ����� ������: {SectorOfficers.Count} ����");
                    }
                }

                // ����� ������� ��������
                for (int i = 0; i < codes.Count && i < VisitorInputs.Count; i++)
                {
                    var code = codes[i];
                    var officer = officers.ElementAtOrDefault(i); // استخدام الفهرس بدلاً من البحث بالكود

                    if (officer != null)
                    {
                        // ����� �� ������ �� ����� ����� ������ �������
                        var matchingOfficer = SectorOfficers.FirstOrDefault(o => o.Id == officer.Id);
                        if (matchingOfficer != null)
                        {
                            VisitorInputs[i].SelectedOfficer = matchingOfficer;
                            System.Diagnostics.Debug.WriteLine($"? �� ����� ����� {code} -> {officer.Name}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"?? ������ {officer.Name} ��� ����� �� ����� ����� ������");

                            // ��� �� ���� �� ������ɡ ���� ������
                            SectorOfficers.Add(officer);
                            VisitorInputs[i].SelectedOfficer = officer;
                            System.Diagnostics.Debug.WriteLine($"?? �� ����� ������ ������: {officer.Name}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"? ����� {code} ��� ����� �� ����� ��������");
                    }
                }

                // تحديث الواجهة والخصائص
                RaisePropertyChanged(nameof(SectorOfficers));
                RaisePropertyChanged(nameof(VisitorInputs));

                // تحديث إضافي للتأكد من تحديث الواجهة
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    // تحديث شامل للقوائم
                    RaisePropertyChanged(nameof(SectorOfficers));
                    RaisePropertyChanged(nameof(VisitorInputs));

                    // إجبار تحديث كل عنصر في القائمة
                    for (int i = 0; i < VisitorInputs.Count; i++)
                    {
                        var temp = VisitorInputs[i].SelectedOfficer;
                        VisitorInputs[i].SelectedOfficer = null;
                        VisitorInputs[i].SelectedOfficer = temp;
                    }
                }));

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث الواجهة والموظفين");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ������ ����� �������� ��������: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private void CalculateDays()
        {
            // �� �������� ���� ����� ����� �������� ���� ������
            DateTime start = DepartureDate.Date;
            DateTime end = ReturnDate.Date;

            int newDaysCount;

            // ��� ��� ����� ������ ��� ����� ������
            if (end < start)
            {
                newDaysCount = 0;
                System.Diagnostics.Debug.WriteLine("?? ����� ������ ��� �� ����� ������ - ��� ������ = 0");
            }
            else
            {
                // ��� +1 ����� ����� ���� ����� (����: �� 1 ��� 1 = 1 ���)
                newDaysCount = (end - start).Days + 1;

                System.Diagnostics.Debug.WriteLine($"?? ���� ������: �� {start:dd/MM/yyyy} ��� {end:dd/MM/yyyy} = {newDaysCount} ����");
            }

            // ����� ������ �� ����� �������
            if (_daysCount != newDaysCount)
            {
                _daysCount = newDaysCount;
                RaisePropertyChanged(nameof(DaysCount));
                // �� ����� ����� ������ �� ��� ������
                System.Diagnostics.Debug.WriteLine($"? �� ����� ��� ������ ���: {newDaysCount} (�� ����� �����)");
            }
        }

        private async void LoadData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting LoadData...");
                StatusMessage = "���� ����� ��������...";

                // Load sectors
                System.Diagnostics.Debug.WriteLine("Loading sectors...");
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }
                System.Diagnostics.Debug.WriteLine($"Loaded {Sectors.Count} sectors");

                // Load projects
                System.Diagnostics.Debug.WriteLine("Loading projects...");
                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }
                System.Diagnostics.Debug.WriteLine($"Loaded {Projects.Count} projects");

                // Load field visits
                System.Diagnostics.Debug.WriteLine("Loading field visits...");
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits)
                {
                    FieldVisits.Add(visit);
                }
                System.Diagnostics.Debug.WriteLine($"Loaded {FieldVisits.Count} field visits");

                // ��� ���� ����� �������� ����ɡ ���� ������ �������
                if (FieldVisits.Count == 0 && Sectors.Count == 0 && Projects.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("Database is empty, creating sample data...");
                    StatusMessage = "����� �������� ����ɡ ���� ����� ������ �������...";
                    await CreateSampleDataAsync();
                }

                UpdateStatistics();
                CheckVisitNumberDuplicate(); // ��� ������� ��� ����� ��������
                StatusMessage = $"�� ����� {FieldVisits.Count} ����� �������";
                System.Diagnostics.Debug.WriteLine("LoadData completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadData: {ex.Message}");
                StatusMessage = "��� �� ����� ��������";

                // Don't show MessageBox during initialization as it might cause issues
                // MessageBox.Show($"? ��� �� ����� ��������: {ex.Message}", "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateSampleDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Creating sample data...");

                // ����� ������ �������
                var sampleSectors = new List<Sector>
                {
                    new Sector { Id = 1, Name = "���� �����", Code = "SAN" },
                    new Sector { Id = 2, Name = "���� ���", Code = "ADE" },
                    new Sector { Id = 3, Name = "���� ���", Code = "TAI" },
                    new Sector { Id = 4, Name = "���� �������", Code = "HOD" }
                };

                foreach (var sector in sampleSectors)
                {
                    await _dataService.AddSectorAsync(sector);
                    Sectors.Add(sector);
                }

                // ����� ������ �������
                var sampleProjects = new List<Project>
                {
                    new Project { Id = 1, ProjectName = "����� ����� ��������� �������", ProjectNumber = "RCD001" },
                    new Project { Id = 2, ProjectName = "����� ��� �������� �������", ProjectNumber = "SME002" },
                    new Project { Id = 3, ProjectName = "����� ������� �������", ProjectNumber = "LCD003" },
                    new Project { Id = 4, ProjectName = "����� ����� �������", ProjectNumber = "FSP004" }
                };

                foreach (var project in sampleProjects)
                {
                    await _dataService.AddProjectAsync(project);
                    Projects.Add(project);
                }

                // ����� ������ ������� �������
                var sampleVisits = new List<FieldVisit>
                {
                    new FieldVisit
                    {
                        Id = 1,
                        VisitNumber = "001/2025",
                        AddDate = DateTime.Now.AddDays(-10),
                        DepartureDate = DateTime.Now.AddDays(-8),
                        ReturnDate = DateTime.Now.AddDays(-6),
                        DaysCount = 3,
                        MissionPurpose = "����� ������� ������� ����� ��������",
                        SectorId = 1,
                        VisitorsCount = 2,
                        SelectedDrivers = "���� ���� ������",
                        DriverContract = "��� ��� 001/2025"
                    },
                    new FieldVisit
                    {
                        Id = 2,
                        VisitNumber = "002/2025",
                        AddDate = DateTime.Now.AddDays(-5),
                        DepartureDate = DateTime.Now.AddDays(-3),
                        ReturnDate = DateTime.Now.AddDays(-1),
                        DaysCount = 2,
                        MissionPurpose = "����� ����� �������� �������",
                        SectorId = 2,
                        VisitorsCount = 3,
                        SelectedDrivers = "����� ��� �������",
                        DriverContract = "��� ��� 002/2025"
                    }
                };

                foreach (var visit in sampleVisits)
                {
                    await _dataService.AddFieldVisitAsync(visit);
                    FieldVisits.Add(visit);
                }

                System.Diagnostics.Debug.WriteLine("Sample data created successfully");
                StatusMessage = "�� ����� �������� ��������� �����";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating sample data: {ex.Message}");
                StatusMessage = "��� �� ����� �������� ���������";
            }
        }

        private async void ForceRefreshData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("?? === ��� ����� ����� �������� ���� ===");
                StatusMessage = "���� ����� ����� ��������...";

                // ����� ����� ���� �������� �� ����� ��������
                System.Diagnostics.Debug.WriteLine("?? ����� ��������...");
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }
                System.Diagnostics.Debug.WriteLine($"? �� ����� {Sectors.Count} ����");

                System.Diagnostics.Debug.WriteLine("?? ����� ��������...");
                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }
                System.Diagnostics.Debug.WriteLine($"? �� ����� {Projects.Count} �����");

                System.Diagnostics.Debug.WriteLine("?? ����� �������� ���������...");
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits)
                {
                    FieldVisits.Add(visit);
                }
                System.Diagnostics.Debug.WriteLine($"? �� ����� {FieldVisits.Count} ����� �������");

                // ����� ����������
                UpdateStatistics();

                // ����� ����� ������
                StatusMessage = $"? �� ����� �������� - {FieldVisits.Count} ����� �������";

                // ����� ������� ��������
                RaisePropertyChanged(nameof(FieldVisits));
                RaisePropertyChanged(nameof(Sectors));
                RaisePropertyChanged(nameof(Projects));

                System.Diagnostics.Debug.WriteLine("?? === ������ ����� ����� �������� ===");

                // ����� ����� ���� ��������
                MessageBox.Show($"? �� ����� �������� �����!\n\n" +
                              $"?? ��������: {Sectors.Count}\n" +
                              $"??? ��������: {Projects.Count}\n" +
                              $"?? �������� ���������: {FieldVisits.Count}",
                              "����� ��������", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ����� ����� ��������: {ex.Message}");
                StatusMessage = $"? ��� �� ����� ��������: {ex.Message}";
                MessageBox.Show($"? ��� �� ����� ��������:\n{ex.Message}",
                              "���", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("?? === ��� ����� ����� �������� ���� ===");
                StatusMessage = "���� ����� ����� ��������...";

                // ����� ����� ���� �������� �� ����� ��������
                System.Diagnostics.Debug.WriteLine("?? ����� ��������...");
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }
                System.Diagnostics.Debug.WriteLine($"? �� ����� {Sectors.Count} ����");

                System.Diagnostics.Debug.WriteLine("?? ����� ��������...");
                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }
                System.Diagnostics.Debug.WriteLine($"? �� ����� {Projects.Count} �����");

                System.Diagnostics.Debug.WriteLine("?? ����� �������� ���������...");
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits)
                {
                    FieldVisits.Add(visit);
                }
                System.Diagnostics.Debug.WriteLine($"? �� ����� {FieldVisits.Count} ����� �������");

                // ����� ����������
                UpdateStatistics();

                // ����� ����� ������
                StatusMessage = $"? �� ����� �������� - {FieldVisits.Count} ����� �������";

                // ����� ������� ��������
                RaisePropertyChanged(nameof(FieldVisits));
                RaisePropertyChanged(nameof(Sectors));
                RaisePropertyChanged(nameof(Projects));

                System.Diagnostics.Debug.WriteLine("?? === ������ ����� ����� �������� ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ����� ����� ��������: {ex.Message}");
                StatusMessage = $"? ��� �� ����� ��������: {ex.Message}";
            }
        }

        private async Task RefreshProjectsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Refreshing projects...");

                // ����� ����� �������� �� ����� ��������
                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }

                System.Diagnostics.Debug.WriteLine($"Refreshed {Projects.Count} projects");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing projects: {ex.Message}");
            }
        }

        private void GenerateVisitNumber()
        {
            // �� ����� ������ ��� ������ ��� ���� - �������� ������ ������
            VisitNumber = string.Empty;
        }

        private void CheckVisitNumberDuplicate()
        {
            if (string.IsNullOrWhiteSpace(VisitNumber))
            {
                IsVisitNumberDuplicate = false;
                return;
            }

            // ��� ������� �� ������� ������� ������� �� ���� �������
            var isDuplicate = FieldVisits.Any(v => v.VisitNumber.Equals(VisitNumber, StringComparison.OrdinalIgnoreCase)
                                                  && (SelectedFieldVisit == null || v.Id != SelectedFieldVisit.Id));
            IsVisitNumberDuplicate = isDuplicate;
        }

        private void UpdateHijriDate()
        {
            try
            {
                var hijriCalendar = new System.Globalization.HijriCalendar();
                var hijriYear = hijriCalendar.GetYear(AddDate);
                var hijriMonth = hijriCalendar.GetMonth(AddDate);
                var hijriDay = hijriCalendar.GetDayOfMonth(AddDate);

                string[] hijriMonthNames = {
                    "����", "���", "���� �����", "���� �����", "����� ������", "����� ������",
                    "���", "�����", "�����", "����", "�� ������", "�� �����"
                };

                var monthName = hijriMonth <= hijriMonthNames.Length ? hijriMonthNames[hijriMonth - 1] : hijriMonth.ToString();
                HijriDate = $"{hijriDay} {monthName} {hijriYear} ��";
            }
            catch
            {
                HijriDate = "��� ����";
            }
        }

        private async void LoadSectorOfficers()
        {
            await UpdateSectorOfficersAsync();
        }

        private async Task UpdateSectorOfficersAsync()
        {
            if (SelectedSector == null)
            {
                SectorOfficers.Clear();
                return;
            }



            try
            {
                var officers = await _dataService.GetOfficersBySectorAsync(SelectedSector.Id);
                SectorOfficers.Clear();
                foreach (var officer in officers)
                {
                    SectorOfficers.Add(officer);
                }
                System.Diagnostics.Debug.WriteLine($"Updated sector officers: {SectorOfficers.Count} officers for sector {SelectedSector.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating sector officers: {ex.Message}");
            }
        }

        private void UpdateVisitorInputs()
        {
            VisitorInputs.Clear();
            for (int i = 1; i <= VisitorsCount; i++)
            {
                VisitorInputs.Add(new VisitorInput { Number = $"{i}." });
            }
        }

        private void UpdateItineraryDays()
        {
            // �������� ��������� ��������
            var existingItinerary = ItineraryDays.ToDictionary(d => d.DayNumber, d => d.Itinerary);

            ItineraryDays.Clear();
            for (int i = 1; i <= DaysCount; i++)
            {
                var itineraryDay = new ItineraryDay
                {
                    DayNumber = i,
                    Itinerary = existingItinerary.ContainsKey(i) ? existingItinerary[i] : string.Empty
                };
                ItineraryDays.Add(itineraryDay);
            }

            System.Diagnostics.Debug.WriteLine($"Updated itinerary days: {ItineraryDays.Count} days for {DaysCount} total days");
        }

        private void UpdateProjectInputs(bool clearExistingData = false)
        {
            Dictionary<int, ProjectInput> existingProjects = new Dictionary<int, ProjectInput>();

            // �������� ��������� �������� ��� ��� �� ���� �������
            if (!clearExistingData && ProjectInputs.Any())
            {
                existingProjects = ProjectInputs.ToDictionary(p => int.Parse(p.Number.TrimEnd('.')), p => p);
            }

            ProjectInputs.Clear();
            for (int i = 1; i <= ProjectsCount; i++)
            {
                var projectInput = new ProjectInput
                {
                    Number = $"{i}."
                };

                // ������� �������� �������� �� ���� ���� �� ���� �������
                if (!clearExistingData && existingProjects.ContainsKey(i))
                {
                    var existing = existingProjects[i];
                    projectInput.ProjectNumber = existing.ProjectNumber;
                    projectInput.ProjectName = existing.ProjectName;
                    projectInput.ProjectDays = existing.ProjectDays;
                    projectInput.IsProjectFound = existing.IsProjectFound;
                }

                ProjectInputs.Add(projectInput);
            }

            System.Diagnostics.Debug.WriteLine($"Updated project inputs: {ProjectInputs.Count} projects for {ProjectsCount} total projects (clearExistingData: {clearExistingData})");
        }

        private async void SearchProjectByNumber(ProjectInput projectInput)
        {
            if (string.IsNullOrWhiteSpace(projectInput.ProjectNumber))
            {
                projectInput.ProjectName = string.Empty;
                projectInput.IsProjectFound = true;
                return;
            }



            try
            {
                var project = await _dataService.GetProjectByNumberAsync(projectInput.ProjectNumber);
                if (project != null)
                {
                    projectInput.ProjectName = project.ProjectName;
                    projectInput.IsProjectFound = true;
                }
                else
                {
                    projectInput.ProjectName = "������� ��� �����";
                    projectInput.IsProjectFound = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error searching project: {ex.Message}");
                projectInput.ProjectName = "��� �� �����";
                projectInput.IsProjectFound = false;
            }
        }

        private void ValidateProjectDays()
        {
            // ������ �� �� ����� ���� �������� = ���� �������
            var totalProjectDays = ProjectInputs.Sum(p => p.ProjectDays);

            if (totalProjectDays != DaysCount && totalProjectDays > 0)
            {
                System.Diagnostics.Debug.WriteLine($"Project days validation failed: Total project days ({totalProjectDays}) != Visit days ({DaysCount})");
            }
        }

        private async Task LoadFieldVisitDetailsAsync()
        {
            if (SelectedFieldVisit == null) return;



            try
            {
                VisitNumber = SelectedFieldVisit.VisitNumber;
                AddDate = SelectedFieldVisit.AddDate;
                HijriDate = SelectedFieldVisit.HijriDate;
                DepartureDate = SelectedFieldVisit.DepartureDate;
                ReturnDate = SelectedFieldVisit.ReturnDate;

                // ����� ���� ��� ������ ����� �� ������� ������ ��������
                CalculateDays();

                MissionPurpose = SelectedFieldVisit.MissionPurpose;
                VisitorsCount = SelectedFieldVisit.VisitorsCount;

                // Set selected sector first
                SelectedSector = Sectors.FirstOrDefault(s => s.Id == SelectedFieldVisit.SectorId);

                // Update sector officers after setting sector and WAIT for completion
                await UpdateSectorOfficersAsync();

                // Load visitors after updating officers
                VisitorInputs.Clear();
                for (int i = 0; i < SelectedFieldVisit.Visitors.Count; i++)
                {
                    var visitor = SelectedFieldVisit.Visitors[i];
                    var visitorInput = new VisitorInput
                    {
                        Number = $"{i + 1}.",
                        SelectedOfficer = SectorOfficers.FirstOrDefault(o => o.Id == visitor.OfficerId)
                    };
                    VisitorInputs.Add(visitorInput);
                }

                // ����� ������ ����� ��� ��� ��� ��������� ���� �� ��� ������ ���������
                while (VisitorInputs.Count < VisitorsCount)
                {
                    VisitorInputs.Add(new VisitorInput { Number = $"{VisitorInputs.Count + 1}." });
                }

                // Load itinerary
                ItineraryDays.Clear();
                for (int i = 1; i <= DaysCount; i++)
                {
                    var itinerary = i <= SelectedFieldVisit.Itinerary.Count ? SelectedFieldVisit.Itinerary[i - 1] : string.Empty;
                    ItineraryDays.Add(new ItineraryDay
                    {
                        DayNumber = i,
                        Itinerary = itinerary
                    });
                }

                // Load projects
                ProjectsCount = SelectedFieldVisit.Projects?.Count ?? 0;
                ProjectInputs.Clear();

                if (SelectedFieldVisit.Projects != null && SelectedFieldVisit.Projects.Any())
                {
                    for (int i = 0; i < SelectedFieldVisit.Projects.Count; i++)
                    {
                        var project = SelectedFieldVisit.Projects[i];
                        var projectInput = new ProjectInput
                        {
                            Number = $"{i + 1}.",
                            ProjectNumber = project.ProjectNumber ?? string.Empty,
                            ProjectName = project.ProjectName ?? string.Empty,
                            ProjectDays = project.ProjectDays // ����� ��� ������
                        };
                        ProjectInputs.Add(projectInput);
                    }
                }

                // ����� ������ ����� ��� ��� ��� �������� ���� �� �������� ��������
                while (ProjectInputs.Count < ProjectsCount)
                {
                    ProjectInputs.Add(new ProjectInput { Number = $"{ProjectInputs.Count + 1}." });
                }

                // Load new fields
                SecurityRoute = SelectedFieldVisit.SecurityRoute ?? string.Empty;
                VisitNotes = SelectedFieldVisit.VisitNotes ?? string.Empty;

                // Load Excel fields
                OdkVisitNumber = SelectedFieldVisit.OdkVisitNumber ?? string.Empty;
                ApprovalBy = SelectedFieldVisit.ApprovalBy ?? string.Empty;
                SubmissionTime = SelectedFieldVisit.SubmissionTime;

                System.Diagnostics.Debug.WriteLine($"? �� ����� ������ �������:");
                System.Diagnostics.Debug.WriteLine($"   - ��� ������� ODK: '{OdkVisitNumber}'");
                System.Diagnostics.Debug.WriteLine($"   - �������� ��: '{ApprovalBy}'");
                System.Diagnostics.Debug.WriteLine($"   - ��� �������: '{FormattedSubmissionTime}'");

                // Update button text
                UpdateButtonText();

                System.Diagnostics.Debug.WriteLine($"Loaded field visit details: {SelectedFieldVisit.VisitNumber}, Visitors: {VisitorInputs.Count}, Officers: {SectorOfficers.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading field visit details: {ex.Message}");
            }
        }

        private void UpdateButtonText()
        {
            SaveButtonText = SelectedFieldVisit != null ? "����� �������" : "����� �������";
        }

        public void ClearForm()
        {
            SelectedFieldVisit = null;
            NumericInput = string.Empty;
            AddDate = DateTime.Now;
            DepartureDate = DateTime.Now;
            ReturnDate = DateTime.Now;
            MissionPurpose = string.Empty;
            SelectedSector = null;
            VisitorsCount = 1;
            ProjectsCount = 1; // ����� ����� ��� �������� ��� 1
            SecurityRoute = string.Empty;
            VisitNotes = string.Empty;

            // ����� ������ �������
            ApprovalBy = string.Empty;
            SubmissionTime = null;
            OdkVisitNumber = string.Empty;

            UpdateHijriDate();
            CalculateDays();
            UpdateVisitorInputs();
            UpdateItineraryDays(); // Clear and update itinerary
            UpdateProjectInputs(clearExistingData: true); // Clear and update projects with data clearing
            UpdateButtonText(); // Update button text when clearing form

            System.Diagnostics.Debug.WriteLine("?? �� ����� ������� ������� ��� �� ��� ������ �������");
        }

        private void RaiseCanExecuteChanged()
        {
            AddCommand.RaiseCanExecuteChanged();
            SaveCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
            DecreaseVisitorsCommand.RaiseCanExecuteChanged();
            DecreaseProjectsCommand.RaiseCanExecuteChanged();
            DecreaseItineraryCommand.RaiseCanExecuteChanged();
        }

        private void StartTimeUpdateTimer()
        {
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += (s, e) =>
            {
                CurrentTime = DateTime.Now.ToString("HH:mm:ss");
                CurrentDate = DateTime.Now.ToString("dddd� dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
            };
            timer.Start();
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalParticipants));
            RaisePropertyChanged(nameof(ActiveSectorsCount));
        }

        private async void GenerateDriverContract()
        {
            try
            {
                // ����� ��� ��� ������ �������� �������:
                // ����� ������� (4 �����) + ����� (2 ���) + ��� ������
                var now = DateTime.Now;
                var year = now.Year.ToString(); // ����� ����� (4 �����)
                var month = now.Month.ToString("D2"); // ����� ������

                // ������ ��� ��� ������ �� ��� ����� ������ ����� ��������
                var monthContractsCount = await GetMonthContractsCountAsync();
                var sequentialNumber = (monthContractsCount + 1).ToString(); // ��� ������

                // ����� ��� ����� ������� (����: 2025061)
                DriverContract = $"{year}{month}{sequentialNumber}";

                System.Diagnostics.Debug.WriteLine($"Generated driver contract: {DriverContract}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating driver contract: {ex.Message}");
                var now = DateTime.Now;
                DriverContract = $"{now:yyyyMM}1";
            }
        }

        private async Task<int> GetTodayVisitsCountAsync()
        {
            try
            {
                var today = DateTime.Today;
                var visits = await _dataService.GetFieldVisitsAsync();
                return visits.Count(v => v.AddDate.Date == today);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting today visits count: {ex.Message}");
                return 0;
            }
        }

        private async Task<int> GetMonthContractsCountAsync()
        {
            try
            {
                var fieldVisits = await _dataService.GetFieldVisitsAsync();
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                return fieldVisits.Count(v => v.AddDate.Month == currentMonth && v.AddDate.Year == currentYear);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting month's contracts count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// ����� ������ ������� �� ����� ����� �������� �������
        /// </summary>
        private async Task<List<FieldVisitProject>> CreateFieldVisitProjectsAsync(ObservableCollection<ProjectInput> projectInputs)
        {
            var fieldVisitProjects = new List<FieldVisitProject>();

            try
            {
                // ��� ���� �������� �� ����� ��������
                var allProjects = await _dataService.GetProjectsAsync();

                var validProjects = projectInputs.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber)).ToList();

                for (int i = 0; i < validProjects.Count; i++)
                {
                    var projectInput = validProjects[i];

                    // ����� �� ������� �� ����� ��������
                    var existingProject = allProjects.FirstOrDefault(p =>
                        p.ProjectNumber.Equals(projectInput.ProjectNumber, StringComparison.OrdinalIgnoreCase));

                    var fieldVisitProject = new FieldVisitProject
                    {
                        Id = 0, // ���� ����
                        FieldVisitId = 0, // ���� ������ ��� �����
                        ProjectId = existingProject?.Id, // ��� �������� ������� �� ���
                        ProjectNumber = projectInput.ProjectNumber,
                        ProjectName = projectInput.ProjectName,
                        ProjectDays = projectInput.ProjectDays, // ��� ��� ������
                        DisplayOrder = i + 1,
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    fieldVisitProjects.Add(fieldVisitProject);

                    System.Diagnostics.Debug.WriteLine($"?? ������� {i + 1}: ���='{projectInput.ProjectNumber}', " +
                        $"���='{projectInput.ProjectName}', ����={projectInput.ProjectDays}, �����={existingProject != null}");
                }

                System.Diagnostics.Debug.WriteLine($"? �� ����� {fieldVisitProjects.Count} ����� �������");
                return fieldVisitProjects;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ����� ������ �������: {ex.Message}");

                // �� ���� ����á ����� �������� ���� ���
                return projectInputs.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber))
                                   .Select((p, index) => new FieldVisitProject
                                   {
                                       Id = 0,
                                       FieldVisitId = 0,
                                       ProjectId = null,
                                       ProjectNumber = p.ProjectNumber,
                                       ProjectName = p.ProjectName,
                                       ProjectDays = p.ProjectDays, // ��� ��� ������
                                       DisplayOrder = index + 1,
                                       IsActive = true,
                                       CreatedAt = DateTime.Now
                                   }).ToList();
            }
        }

        #endregion

        /// <summary>
        /// ������ �� ���� ��� ������� �� ������ ���� ����� ��� �� ��� �������
        /// </summary>
        private async Task CheckVisitNumberExistenceAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(VisitNumber))
                    return;

                System.Diagnostics.Debug.WriteLine($"?? ������ �� ���� ��� �������: {VisitNumber}");

                // ��� ���� �������� ������ �� ���� �����
                var allVisits = await _dataService.GetFieldVisitsAsync();

                // ������ �� ���� ��� ������� (�� ������� ������� ������� �� ���� �������)
                var existingVisit = allVisits.FirstOrDefault(v =>
                    v.VisitNumber.Equals(VisitNumber, StringComparison.OrdinalIgnoreCase) &&
                    (SelectedFieldVisit == null || v.Id != SelectedFieldVisit.Id));

                if (existingVisit != null)
                {
                    // ��� ������� ����� ������
                    var message = $"?? �����: ��� ������� '{VisitNumber}' ����� ������ �� ������\n\n";
                    message += $"?? ������ ������� ��������:\n";
                    message += $"� ����� �������: {existingVisit.AddDate:yyyy/MM/dd}\n";
                    message += $"� ������: {existingVisit.SectorName}\n";
                    message += $"� ���� ������: {existingVisit.MissionPurpose}\n\n";
                    message += $"?? �� ���� �������� ���� �����";

                    var result = MessageBox.Show(message, "��� ������� �����",
                        MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (result == MessageBoxResult.No)
                    {
                        // �������� ����� ��� �������� - ������� ��� ��� ��� �������
                        System.Diagnostics.Debug.WriteLine("? �������� ����� ��� �������� ���� ����� ��� �������");
                        return;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("? �������� ����� �������� ��� ����� ��� �������");
                    }
                }
                else
                {
                    // ��� ������� ��� ����� - ��� ���� ����
                    ShowVisitNotFoundDialog(VisitNumber);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ������ �� ��� �������: {ex.Message}");
                // �� ���� ����á ����� ������� ���� ����
            }
        }

        /// <summary>
        /// ��� ���� ���� ����� �� ���� ����� ������ ������
        /// </summary>
        private void ShowVisitNotFoundDialog(string visitNumber)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"?? ��� ���� ��� ���� ����� �����: {visitNumber}");

                // ����� ����� ����� ���� ����� ��� ���� �������
                var dialog = new Window
                {
                    Title = "�� ���� �����",
                    Width = 500,
                    Height = 400,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Application.Current.MainWindow,
                    ResizeMode = ResizeMode.NoResize,
                    WindowStyle = WindowStyle.ToolWindow
                };

                // ����� �������
                var mainGrid = new Grid();
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(80) }); // Header
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Icon
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Message
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Details
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(80) }); // Buttons

                // Header �� ����� �����
                var headerBorder = new Border
                {
                    Background = new LinearGradientBrush(Colors.Orange, Colors.DarkOrange, 90),
                    CornerRadius = new CornerRadius(5, 5, 0, 0)
                };
                Grid.SetRow(headerBorder, 0);

                var headerText = new TextBlock
                {
                    Text = "?? ����� ����� �� �������",
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
                headerBorder.Child = headerText;

                // ������ �����
                var iconText = new TextBlock
                {
                    Text = "?",
                    FontSize = 48,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 10)
                };
                Grid.SetRow(iconText, 1);

                // ������� ��������
                var messageText = new TextBlock
                {
                    Text = $"�� ���� ����� ����: {visitNumber}",
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 15)
                };
                Grid.SetRow(messageText, 2);

                // ��������
                var detailsPanel = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(20, 0, 20, 20)
                };

                detailsPanel.Children.Add(new TextBlock
                {
                    Text = "?? ������ �����:",
                    FontWeight = FontWeights.Bold,
                    Margin = new Thickness(0, 0, 0, 10),
                    HorizontalAlignment = HorizontalAlignment.Right
                });

                detailsPanel.Children.Add(new TextBlock
                {
                    Text = $"� ��� ������� �������: {visitNumber}",
                    Margin = new Thickness(0, 0, 0, 5),
                    HorizontalAlignment = HorizontalAlignment.Right
                });

                detailsPanel.Children.Add(new TextBlock
                {
                    Text = "� ���� �����: �� ���� �����",
                    Margin = new Thickness(0, 0, 0, 5),
                    HorizontalAlignment = HorizontalAlignment.Right
                });

                detailsPanel.Children.Add(new TextBlock
                {
                    Text = "� ������� �������: ����� ����� �����",
                    Margin = new Thickness(0, 0, 0, 15),
                    HorizontalAlignment = HorizontalAlignment.Right
                });

                detailsPanel.Children.Add(new TextBlock
                {
                    Text = "?? �� ���� �������� ������ ����� ����� ���� �����",
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.DarkBlue,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextWrapping = TextWrapping.Wrap
                });

                Grid.SetRow(detailsPanel, 3);

                // �������
                var buttonsPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 0)
                };

                var continueButton = new Button
                {
                    Content = "? ������ �������",
                    Width = 120,
                    Height = 35,
                    Margin = new Thickness(10, 0, 10, 0),
                    Background = new SolidColorBrush(Colors.Green),
                    Foreground = Brushes.White,
                    FontWeight = FontWeights.Bold
                };

                var cancelButton = new Button
                {
                    Content = "? �����",
                    Width = 100,
                    Height = 35,
                    Margin = new Thickness(10, 0, 10, 0),
                    Background = new SolidColorBrush(Colors.Red),
                    Foreground = Brushes.White,
                    FontWeight = FontWeights.Bold
                };

                continueButton.Click += (s, e) => {
                    dialog.DialogResult = true;
                    dialog.Close();
                };

                cancelButton.Click += (s, e) => {
                    dialog.DialogResult = false;
                    dialog.Close();
                };

                buttonsPanel.Children.Add(continueButton);
                buttonsPanel.Children.Add(cancelButton);
                Grid.SetRow(buttonsPanel, 4);

                // ����� ���� ������� ������
                mainGrid.Children.Add(headerBorder);
                mainGrid.Children.Add(iconText);
                mainGrid.Children.Add(messageText);
                mainGrid.Children.Add(detailsPanel);
                mainGrid.Children.Add(buttonsPanel);

                dialog.Content = mainGrid;

                // ��� ������� ������� ��� �������
                var result = dialog.ShowDialog();

                if (result == true)
                {
                    System.Diagnostics.Debug.WriteLine($"? �������� ����� �������� �� ����� ������: {visitNumber}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("? �������� ����� ��� �������� �� ����� ������");
                    // ���� ����� ���� ����� ��� ��� ��� �����
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"? ��� �� ��� ���� ��� ���� �������: {ex.Message}");
                // �� ���� ����á ��� ����� �����
                MessageBox.Show($"?? ������: ��� ������� '{visitNumber}' ��� ����� �� ������\n\n? ���� ����� ����� ����� ���� �����",
                    "��� ����� ����", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void OnDatabaseRefreshed()
        {
            try
            {
                // Reload look?ups from the shared data service
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var s in sectors) Sectors.Add(s);
                RaisePropertyChanged(nameof(Sectors));

                var officers = await _dataService.GetOfficersAsync();
                SectorOfficers.Clear();
                foreach (var o in officers) SectorOfficers.Add(o);
                RaisePropertyChanged(nameof(SectorOfficers));

                var projects = await _dataService.GetProjectsAsync();
                Projects.Clear();
                foreach (var p in projects) Projects.Add(p);
                RaisePropertyChanged(nameof(Projects));

                StatusMessage = "? �� ����� �������� �����";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reloading data: {ex.Message}");
            }
        }

        /// <summary>
        /// ?????? ?? ??? ?????? ????????
        /// </summary>


        /// <summary>
        /// ������ �� ��� ������ ��������
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateProjectsData()
        {
            try
            {
                // �� ������ �����ء ����� ��� �� ���� ������ �������� ��� �� �������
                var projectsWithData = ProjectInputs.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber) || !string.IsNullOrWhiteSpace(p.ProjectName)).ToList();

                // ��� �� ��� ����� �� �����ڡ ���� �����
                if (!projectsWithData.Any())
                {
                    return (true, string.Empty);
                }

                foreach (var project in projectsWithData)
                {
                    if (string.IsNullOrWhiteSpace(project.ProjectNumber))
                    {
                        return (false, "��� ����� ��� ������� ����� �������� �������");
                    }

                    if (string.IsNullOrWhiteSpace(project.ProjectName))
                    {
                        return (false, $"��� ����� ��� ������� ������� ���: {project.ProjectNumber}");
                    }
                }

                // ������ �� ��� ����� ����� ��������
                var projectNumbers = projectsWithData.Select(p => p.ProjectNumber).ToList();
                var duplicates = projectNumbers.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key).ToList();

                if (duplicates.Any())
                {
                    return (false, $"����� �������� ������� �����: {string.Join(", ", duplicates)}");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"��� �� ������ �� ������ ��������: {ex.Message}");
            }
        }

}
}








